'use client';

import React, { useState, useEffect } from 'react';
import { Search, Filter, Download, Plus, Shield, UserCheck, Award } from 'lucide-react';
import DataTable from '@/components/admin/common/DataTable';
import { userService } from '@/services/userService';
import { AdminUser, DataTableColumn } from '@/types/admin';

const UsersPage: React.FC = () => {
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [verificationFilter, setVerificationFilter] = useState(''); // 新增认证状态筛选
  const [pagination, setPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 20
  });

  const columns: DataTableColumn<AdminUser>[] = [
    {
      key: 'username',
      title: 'User Info',
      render: (_, record) => (
        <div className="flex items-center">
          <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-medium">
            {record.display_name.charAt(0).toUpperCase()}
          </div>
          <div className="ml-3">
            <div className="text-sm font-medium text-gray-900">{record.display_name}</div>
            <div className="text-sm text-gray-500">{record.email}</div>
          </div>
        </div>
      )
    },
    {
      key: 'role',
      title: 'Role',
      sortable: true,
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === 'super_admin' ? 'bg-purple-100 text-purple-800' :
          value === 'admin' ? 'bg-blue-100 text-blue-800' :
          value === 'moderator' ? 'bg-green-100 text-green-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {value === 'super_admin' ? 'Super Admin' :
           value === 'admin' ? 'Admin' :
           value === 'moderator' ? 'Moderator' : 'User'}
        </span>
      )
    },
    {
      key: 'is_active',
      title: 'Status',
      sortable: true,
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {value ? 'Active' : 'Inactive'}
        </span>
      )
    },
    {
      key: 'verification_status', // 新增认证状态列
      title: 'Verification',
      sortable: true,
      render: (_, record) => {
        // 根据用户属性判断认证状态
        const isVerified = record.email_verified || record.is_verified;
        const isPremium = record.is_premium || record.membership_level > 0;
        const isExpert = record.is_expert || record.expert_verified;
        
        return (
          <div className="flex space-x-1">
            {isVerified && (
              <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                <UserCheck className="w-3 h-3 mr-1" />
                Email Verified
              </span>
            )}
            {isPremium && (
              <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                <Award className="w-3 h-3 mr-1" />
                Premium User
              </span>
            )}
            {isExpert && (
              <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                <Shield className="w-3 h-3 mr-1" />
                Expert Verified
              </span>
            )}
            {!isVerified && !isPremium && !isExpert && (
              <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                Unverified
              </span>
            )}
          </div>
        );
      }
    },
    {
      key: 'stats',
      title: 'Statistics',
      render: (stats) => (
        <div className="text-sm text-gray-900">
          <div>Articles: {stats?.articles || 0}</div>
          <div>Comments: {stats?.comments || 0}</div>
        </div>
      )
    },
    {
      key: 'created_at',
      title: 'Registered',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString('en-US')
    },
    {
      key: 'last_login_at',
      title: 'Last Login',
      sortable: true,
      render: (value) => value ? new Date(value).toLocaleDateString('en-US') : 'Never logged in'
    },
    {
      key: 'id',
      title: 'Actions',
      render: (_, record) => (
        <div className="flex space-x-2">
          <select
            value={record.role}
            onChange={(e) => handleRoleChange(record.id, e.target.value)}
            className="text-xs border rounded px-1 py-0.5"
          >
            <option value="user">User</option>
            <option value="moderator">Moderator</option>
            <option value="admin">Admin</option>
            <option value="super_admin">Super Admin</option>
          </select>
          <button 
            onClick={(e) => {
              e.preventDefault();
              handleUserStatusToggle(record.id, record.is_active);
            }}
            className={`text-sm px-2 py-1 rounded ${
              record.is_active 
                ? 'bg-red-100 text-red-600 hover:bg-red-200' 
                : 'bg-green-100 text-green-600 hover:bg-green-200'
            }`}
          >
            {record.is_active ? 'Deactivate' : 'Activate'}
          </button>
        </div>
      )
    }
  ];

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await userService.getUserList({
        page: pagination.current,
        limit: pagination.pageSize,
        search: searchQuery,
        role: roleFilter,
        status: statusFilter
      });
      
      if (response.success && response.data) {
        setUsers(response.data);
        setPagination(prev => ({
          ...prev,
          total: response.pagination?.total || 0
        }));
      }
    } catch (error) {
      console.error('Failed to fetch user list:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [pagination.current, searchQuery, roleFilter, statusFilter]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchUsers();
  };

  const handleBulkAction = async (action: string) => {
    if (selectedUsers.length === 0) {
      alert('Please select users first');
      return;
    }
    
    if (!confirm(`Are you sure you want to ${action} ${selectedUsers.length} users?`)) {
      return;
    }
    
    try {
      await userService.bulkUpdateUsers(selectedUsers, action);
      
      // 更新本地数据
      if (action === 'activate') {
        setUsers(prev => prev.map(user => 
          selectedUsers.includes(user.id) ? { ...user, is_active: true } : user
        ));
      } else if (action === 'deactivate') {
        setUsers(prev => prev.map(user => 
          selectedUsers.includes(user.id) ? { ...user, is_active: false } : user
        ));
      }
      
      setSelectedUsers([]);
      alert(`Bulk operation successful, affected ${selectedUsers.length} users`);
    } catch (error) {
      console.error('Bulk operation failed:', error);
      alert('Bulk operation failed, please try again');
    }
  };

  const handleUserStatusToggle = async (userId: string, currentStatus: boolean) => {
    try {
      await userService.updateUserStatus(userId, !currentStatus);
      // 更新本地数据
      setUsers(prev => prev.map(user => 
        user.id === userId ? { ...user, is_active: !currentStatus } : user
      ));
      alert(`User status has been ${!currentStatus ? 'activated' : 'deactivated'}`);
    } catch (error) {
      console.error('Failed to update user status:', error);
      alert('Operation failed, please try again');
    }
  };

  const handleRoleChange = async (userId: string, newRole: string) => {
    try {
      await userService.updateUserRole(userId, newRole);
      setUsers(prev => prev.map(user => 
        user.id === userId ? { ...user, role: newRole as any } : user
      ));
      alert(`User role has been updated to ${newRole}`);
    } catch (error) {
      console.error('Failed to update user role:', error);
      alert('Operation failed, please try again');
    }
  };

  // 新增处理认证状态变更的函数
  const handleVerificationChange = async (userId: string, verificationType: string, status: boolean) => {
    try {
      // 在实际应用中，这里应该调用API更新用户的认证状态
      console.log(`更新用户 ${userId} 的 ${verificationType} 认证状态为 ${status}`);
      
      // 模拟更新本地数据
      setUsers(prev => prev.map(user => {
        if (user.id === userId) {
          const updatedUser = { ...user };
          switch (verificationType) {
            case 'email':
              updatedUser.email_verified = status;
              break;
            case 'premium':
              updatedUser.is_premium = status;
              break;
            case 'expert':
              updatedUser.is_expert = status;
              break;
          }
          return updatedUser;
        }
        return user;
      }));
      
      alert('Verification status updated successfully');
    } catch (error) {
      console.error('Failed to update verification status:', error);
      alert('Operation failed, please try again');
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">User Management</h1>
        <div className="flex space-x-3">
          <button className="btn-secondary flex items-center">
            <Download className="w-4 h-4 mr-2" />
            Export Data
          </button>
          <button className="btn-primary flex items-center">
            <Plus className="w-4 h-4 mr-2" />
            Add User
          </button>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSearch} className="flex flex-wrap gap-4">
          <div className="flex-1 min-w-64">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search username, email..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="form-input pl-10"
              />
            </div>
          </div>
          <select
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value)}
            className="form-select w-32"
          >
            <option value="">All Roles</option>
            <option value="user">用户</option>
            <option value="moderator">审核员</option>
            <option value="admin">管理员</option>
            <option value="super_admin">超级管理员</option>
          </select>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="form-select w-32"
          >
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
          {/* 新增认证状态筛选 */}
          <select
            value={verificationFilter}
            onChange={(e) => setVerificationFilter(e.target.value)}
            className="form-select w-32"
          >
            <option value="">All Verification</option>
            <option value="verified">Email Verified</option>
            <option value="premium">Premium User</option>
            <option value="expert">Expert Verified</option>
            <option value="unverified">Unverified</option>
          </select>
          <button type="submit" className="btn-primary flex items-center">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </button>
        </form>
      </div>

      {/* 批量操作 */}
      {selectedUsers.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-800">
              Selected {selectedUsers.length} users
            </span>
            <div className="flex space-x-2">
              <button
                onClick={(e) => {
                  e.preventDefault();
                  handleBulkAction('activate');
                }}
                className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
              >
                Bulk Activate
              </button>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  handleBulkAction('deactivate');
                }}
                className="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700"
              >
                Bulk Deactivate
              </button>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  if (confirm('Are you sure you want to delete selected users? This action cannot be undone!')) {
                    handleBulkAction('delete');
                  }
                }}
                className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
              >
                Bulk Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 用户列表 */}
      <DataTable
        data={users}
        columns={columns}
        loading={loading}
        pagination={{
          current: pagination.current,
          total: pagination.total,
          pageSize: pagination.pageSize,
          onChange: (page) => setPagination(prev => ({ ...prev, current: page }))
        }}
        rowSelection={{
          selectedRowKeys: selectedUsers,
          onChange: setSelectedUsers
        }}
      />
    </div>
  );
};

export default UsersPage;