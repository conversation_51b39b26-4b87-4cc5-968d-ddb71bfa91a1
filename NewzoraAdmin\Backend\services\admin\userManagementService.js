const { User, Article, Comment, sequelize } = require('../../models');
const { Op } = require('sequelize');

class UserManagementService {
  // 获取用户列表
  async getUserList(options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        search = '',
        role = '',
        status = '',
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = options;

      const offset = (page - 1) * limit;
      const whereConditions = {};

      // 搜索条件
      if (search) {
        whereConditions[Op.or] = [
          { username: { [Op.iLike]: `%${search}%` } },
          { email: { [Op.iLike]: `%${search}%` } },
          { display_name: { [Op.iLike]: `%${search}%` } }
        ];
      }

      // 角色筛选
      if (role) {
        whereConditions.role = role;
      }

      // 状态筛选
      if (status === 'active') {
        whereConditions.isActive = true;
      } else if (status === 'inactive') {
        whereConditions.isActive = false;
      }

      const { count, rows: users } = await User.findAndCountAll({
        where: whereConditions,
        attributes: { 
          exclude: ['password_hash'] 
        },
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [[sortBy, sortOrder.toUpperCase()]]
      });

      // 获取用户统计信息
      const usersWithStats = await Promise.all(
        users.map(async (user) => {
          const [articleCount, commentCount] = await Promise.all([
            Article.count({ where: { authorId: user.id } }),
            Comment.count({ where: { authorId: user.id } })
          ]);

          return {
            ...user.toJSON(),
            stats: {
              articles: articleCount,
              comments: commentCount,
              followers: 0, // TODO: 实现关注系统
              following: 0  // TODO: 实现关注系统
            }
          };
        })
      );

      return {
        users: usersWithStats,
        pagination: {
          current: parseInt(page),
          total: count,
          pageSize: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      console.error('获取用户列表失败:', error);
      throw new Error('获取用户列表失败');
    }
  }

  // 获取用户详情
  async getUserDetail(userId) {
    try {
      const user = await User.findByPk(userId, {
        attributes: { 
          exclude: ['password_hash'] 
        }
      });

      if (!user) {
        throw new Error('用户不存在');
      }

      // 获取用户详细统计
      const [
        articles,
        recentComments,
        totalViews,
        totalLikes
      ] = await Promise.all([
        Article.findAll({
          where: { authorId: userId },
          order: [['created_at', 'DESC']],
          limit: 10
        }),
        Comment.findAll({
          where: { authorId: userId },
          include: [{ 
            model: Article, 
            as: 'article',
            attributes: ['title'] 
          }],
          order: [['created_at', 'DESC']],
          limit: 10
        }),
        Article.sum('views', { where: { authorId: userId } }) || 0,
        Article.sum('likes', { where: { authorId: userId } }) || 0
      ]);

      return {
        ...user.toJSON(),
        stats: {
          articles: articles.length,
          comments: recentComments.length,
          followers: 0, // TODO: 实现关注系统
          following: 0, // TODO: 实现关注系统
          totalViews,
          totalLikes
        },
        recentArticles: articles,
        recentComments
      };
    } catch (error) {
      console.error('获取用户详情失败:', error);
      throw error;
    }
  }

  // 更新用户状态
  async updateUserStatus(userId, isActive) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('用户不存在');
      }

      await user.update({ isActive });
      return user.toSafeJSON();
    } catch (error) {
      console.error('更新用户状态失败:', error);
      throw error;
    }
  }

  // 更新用户角色
  async updateUserRole(userId, role) {
    try {
      const validRoles = ['user', 'moderator', 'admin', 'super_admin'];
      if (!validRoles.includes(role)) {
        throw new Error('无效的角色');
      }

      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('用户不存在');
      }

      await user.update({ role });
      return user.toSafeJSON();
    } catch (error) {
      console.error('更新用户角色失败:', error);
      throw error;
    }
  }

  // 删除用户（软删除）
  async deleteUser(userId) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('用户不存在');
      }

      // 软删除：标记为非活跃状态并修改邮箱
      await user.update({ 
        isActive: false,
        email: `deleted_${Date.now()}_${user.email}`
      });

      return user.toSafeJSON();
    } catch (error) {
      console.error('删除用户失败:', error);
      throw error;
    }
  }

  // 批量更新用户
  async bulkUpdateUsers(userIds, action, value) {
    try {
      const validActions = ['activate', 'deactivate', 'delete', 'changeRole'];
      if (!validActions.includes(action)) {
        throw new Error('无效的操作');
      }

      const users = await User.findAll({
        where: { id: { [Op.in]: userIds } }
      });

      if (users.length !== userIds.length) {
        throw new Error('部分用户不存在');
      }

      let updateData = {};
      switch (action) {
        case 'activate':
          updateData = { isActive: true };
          break;
        case 'deactivate':
          updateData = { isActive: false };
          break;
        case 'delete':
          // 批量软删除
          for (const user of users) {
            await user.update({
              isActive: false,
              email: `deleted_${Date.now()}_${user.email}`
            });
          }
          return { updated: users.length };
        case 'changeRole':
          if (!['user', 'moderator', 'admin'].includes(value)) {
            throw new Error('无效的角色');
          }
          updateData = { role: value };
          break;
      }

      const [updatedCount] = await User.update(updateData, {
        where: { id: { [Op.in]: userIds } }
      });

      return { updated: updatedCount };
    } catch (error) {
      console.error('批量更新用户失败:', error);
      throw error;
    }
  }

  // 创建新用户
  async createUser(userData) {
    try {
      const {
        username,
        email,
        password,
        display_name,
        role = 'user',
        bio = ''
      } = userData;

      // 检查用户名和邮箱是否已存在
      const existingUser = await User.findOne({
        where: {
          [Op.or]: [
            { username },
            { email }
          ]
        }
      });

      if (existingUser) {
        throw new Error('用户名或邮箱已存在');
      }

      const user = await User.create({
        username,
        email,
        password_hash: password, // 会在模型的hook中自动加密
        display_name,
        role,
        bio,
        isActive: true,
        isEmailVerified: true // 管理员创建的用户默认已验证
      });

      return user.toSafeJSON();
    } catch (error) {
      console.error('创建用户失败:', error);
      throw error;
    }
  }

  // 获取用户统计概览
  async getUserStats() {
    try {
      const [
        totalUsers,
        activeUsers,
        inactiveUsers,
        adminUsers,
        moderatorUsers,
        regularUsers,
        todayRegistered,
        weekRegistered
      ] = await Promise.all([
        User.count(),
        User.count({ where: { isActive: true } }),
        User.count({ where: { isActive: false } }),
        User.count({ where: { role: 'admin' } }),
        User.count({ where: { role: 'moderator' } }),
        User.count({ where: { role: 'user' } }),
        User.count({
          where: {
            created_at: {
              [Op.gte]: new Date(new Date().setHours(0, 0, 0, 0))
            }
          }
        }),
        User.count({
          where: {
            created_at: {
              [Op.gte]: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            }
          }
        })
      ]);

      return {
        total: totalUsers,
        active: activeUsers,
        inactive: inactiveUsers,
        roles: {
          admin: adminUsers,
          moderator: moderatorUsers,
          user: regularUsers
        },
        recent: {
          today: todayRegistered,
          week: weekRegistered
        }
      };
    } catch (error) {
      console.error('获取用户统计失败:', error);
      throw new Error('获取用户统计失败');
    }
  }
}

module.exports = new UserManagementService();