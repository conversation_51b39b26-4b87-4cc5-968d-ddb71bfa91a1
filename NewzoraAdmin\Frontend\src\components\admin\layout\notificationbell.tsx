'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Bell, Check, X } from 'lucide-react';
import api from '@/lib/api';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  timestamp: string;
  read: boolean;
  actionUrl?: string;
}

const NotificationBell: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const notificationRef = useRef<HTMLDivElement>(null);

  // 初始化通知数据
  useEffect(() => {
    // 模拟初始通知数据
    const initialNotifications: Notification[] = [
      {
        id: '1',
        title: 'New User Registration',
        message: 'User Zhang San has just completed registration',
        type: 'info',
        timestamp: '2分钟前',
        read: false,
        actionUrl: '/admin/users'
      },
      {
        id: '2',
        title: 'Content Pending Review',
        message: 'There are 5 articles waiting for review',
        type: 'warning',
        timestamp: '10分钟前',
        read: false,
        actionUrl: '/admin/content/reviews'
      },
      {
        id: '3',
        title: 'System Update',
        message: 'System will undergo maintenance from 23:00-24:00 tonight',
        type: 'info',
        timestamp: '1小时前',
        read: true
      },
      {
        id: '4',
        title: 'User Report',
        message: 'There are 2 new reports that need to be processed',
        type: 'error',
        timestamp: '2小时前',
        read: false,
        actionUrl: '/admin/users/reports'
      }
    ];

    setNotifications(initialNotifications);
    updateUnreadCount(initialNotifications);
  }, []);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (notificationRef.current && !notificationRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 模拟实时通知
  useEffect(() => {
    // 在实际应用中，这里应该使用 WebSocket 或轮询 API 来获取实时通知
    const interval = setInterval(() => {
      // 模拟新通知
      if (Math.random() > 0.8) {
        const newNotification: Notification = {
          id: Date.now().toString(),
          title: 'New Activity',
          message: 'System detected new user activity',
          type: 'info',
          timestamp: 'Just now',
          read: false
        };
        
        setNotifications(prev => [newNotification, ...prev]);
        setUnreadCount(prev => prev + 1);
      }
    }, 30000); // 每30秒检查一次新通知

    return () => clearInterval(interval);
  }, []);

  const updateUnreadCount = (notifs: Notification[]) => {
    setUnreadCount(notifs.filter(n => !n.read).length);
  };

  const markAsRead = (id: string) => {
    const updatedNotifications = notifications.map(notification =>
      notification.id === id ? { ...notification, read: true } : notification
    );
    
    setNotifications(updatedNotifications);
    updateUnreadCount(updatedNotifications);
  };

  const markAllAsRead = () => {
    const updatedNotifications = notifications.map(notification => ({
      ...notification,
      read: true
    }));
    
    setNotifications(updatedNotifications);
    setUnreadCount(0);
  };

  const removeNotification = (id: string) => {
    const updatedNotifications = notifications.filter(
      notification => notification.id !== id
    );
    
    setNotifications(updatedNotifications);
    updateUnreadCount(updatedNotifications);
  };

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }
    
    if (notification.actionUrl) {
      // 在实际应用中，这里应该使用路由导航
      // window.location.href = notification.actionUrl;
      console.log(`导航到: ${notification.actionUrl}`);
    }
  };

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      case 'warning':
        return 'bg-yellow-500';
      case 'info':
      default:
        return 'bg-blue-500';
    }
  };

  return (
    <div className="relative" ref={notificationRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-1 text-gray-600 hover:text-gray-900 focus:outline-none"
      >
        <Bell size={20} />
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg overflow-hidden z-50">
          <div className="py-2 px-4 bg-gray-50 flex justify-between items-center border-b">
            <h3 className="text-sm font-medium text-gray-900">Notifications</h3>
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="text-xs text-blue-600 hover:text-blue-800 flex items-center"
              >
                <Check size={12} className="mr-1" />
                Mark all as read
              </button>
            )}
          </div>
          
          <div className="max-h-96 overflow-y-auto">
            {notifications.length === 0 ? (
              <div className="py-6 text-center text-gray-500 text-sm">
                No notifications
              </div>
            ) : (
              notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`border-b hover:bg-gray-50 transition-colors duration-200 ${
                    !notification.read ? 'bg-blue-50' : ''
                  }`}
                >
                  <div className="py-3 px-4 flex">
                    <div
                      className={`flex-shrink-0 w-2 h-2 rounded-full mt-2 ${getNotificationIcon(
                        notification.type
                      )}`}
                    ></div>
                    <div className="ml-3 flex-1 min-w-0">
                      <div 
                        className="text-sm font-medium text-gray-900 cursor-pointer"
                        onClick={() => handleNotificationClick(notification)}
                      >
                        {notification.title}
                      </div>
                      <div 
                        className="text-sm text-gray-500 mt-1 cursor-pointer"
                        onClick={() => handleNotificationClick(notification)}
                      >
                        {notification.message}
                      </div>
                      <div className="mt-1 flex justify-between items-center">
                        <span className="text-xs text-gray-400">
                          {notification.timestamp}
                        </span>
                        <div className="flex space-x-2">
                          {!notification.read && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                markAsRead(notification.id);
                              }}
                              className="text-gray-400 hover:text-gray-600"
                              title="Mark as read"
                            >
                              <Check size={14} />
                            </button>
                          )}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              removeNotification(notification.id);
                            }}
                            className="text-gray-400 hover:text-gray-600"
                            title="Delete notification"
                          >
                            <X size={14} />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
          
          <div className="py-2 px-4 bg-gray-50 text-center border-t">
            <button className="text-sm text-blue-600 hover:text-blue-800">
              View all notifications
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationBell;