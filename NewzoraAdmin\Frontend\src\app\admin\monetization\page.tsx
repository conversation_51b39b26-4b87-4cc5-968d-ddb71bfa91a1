'use client';

import React, { useState, useEffect } from 'react';
import { DollarSign, TrendingUp, Users, Eye } from 'lucide-react';
import StatsCard from '@/components/admin/dashboard/StatsCard';
import { dashboardService } from '@/services/dashboardService';

const MonetizationPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');
  
  // 广告收益数据
  const [adRevenueData, setAdRevenueData] = useState([
    { name: '周一', revenue: 1200, impressions: 24000, ctr: 2.5 },
    { name: '周二', revenue: 1800, impressions: 32000, ctr: 3.1 },
    { name: '周三', revenue: 1500, impressions: 28000, ctr: 2.8 },
    { name: '周四', revenue: 2100, impressions: 35000, ctr: 3.5 },
    { name: '周五', revenue: 2400, impressions: 42000, ctr: 3.8 },
    { name: '周六', revenue: 1900, impressions: 31000, ctr: 3.2 },
    { name: '周日', revenue: 1600, impressions: 29000, ctr: 2.9 },
  ]);
  
  // 广告位数据
  const adPlacements = [
    { id: '1', name: '首页横幅', type: '横幅广告', status: '活跃', revenue: 3200, impressions: 64000, ctr: 3.2 },
    { id: '2', name: '文章内嵌', type: '内容广告', status: '活跃', revenue: 2800, impressions: 56000, ctr: 2.8 },
    { id: '3', name: '侧边栏', type: '展示广告', status: '活跃', revenue: 1500, impressions: 30000, ctr: 2.5 },
    { id: '4', name: '弹窗广告', type: '插页广告', status: '暂停', revenue: 800, impressions: 12000, ctr: 4.2 },
  ];
  
  // 提现记录
  const withdrawalRecords = [
    { id: '1', user: '张三', amount: 500, status: '已完成', date: '2024-06-01', method: '支付宝' },
    { id: '2', user: '李四', amount: 320, status: '处理中', date: '2024-06-02', method: '微信支付' },
    { id: '3', user: '王五', amount: 750, status: '已拒绝', date: '2024-06-03', method: '银行卡' },
    { id: '4', user: '赵六', amount: 420, status: '处理中', date: '2024-06-04', method: '支付宝' },
  ];

  useEffect(() => {
    // 模拟数据加载
    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);
    
    return () => clearTimeout(timer);
  }, []);

  const handleTimeRangeChange = (range: string) => {
    setTimeRange(range);
    // 在实际应用中，这里应该重新获取数据
    // 根据时间范围生成不同的示例数据
    switch (range) {
      case '30d':
        setAdRevenueData([
          { name: '第1周', revenue: 8500, impressions: 180000, ctr: 3.2 },
          { name: '第2周', revenue: 9200, impressions: 195000, ctr: 3.5 },
          { name: '第3周', revenue: 7800, impressions: 165000, ctr: 3.1 },
          { name: '第4周', revenue: 10100, impressions: 210000, ctr: 3.8 },
        ]);
        break;
      case '90d':
        setAdRevenueData([
          { name: '第1月', revenue: 32000, impressions: 650000, ctr: 3.1 },
          { name: '第2月', revenue: 38000, impressions: 720000, ctr: 3.4 },
          { name: '第3月', revenue: 42000, impressions: 810000, ctr: 3.7 },
        ]);
        break;
      default: // 7d
        setAdRevenueData([
          { name: '周一', revenue: 1200, impressions: 24000, ctr: 2.5 },
          { name: '周二', revenue: 1800, impressions: 32000, ctr: 3.1 },
          { name: '周三', revenue: 1500, impressions: 28000, ctr: 2.8 },
          { name: '周四', revenue: 2100, impressions: 35000, ctr: 3.5 },
          { name: '周五', revenue: 2400, impressions: 42000, ctr: 3.8 },
          { name: '周六', revenue: 1900, impressions: 31000, ctr: 3.2 },
          { name: '周日', revenue: 1600, impressions: 29000, ctr: 2.9 },
        ]);
    }
  };

  const handleApproveWithdrawal = (id: string) => {
    console.log(`批准提现申请: ${id}`);
    // 在实际应用中，这里应该调用API
  };

  const handleRejectWithdrawal = (id: string) => {
    console.log(`拒绝提现申请: ${id}`);
    // 在实际应用中，这里应该调用API
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">收益管理</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-4"></div>
              <div className="h-8 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">收益管理</h1>
        <div className="flex items-center space-x-2">
          <select 
            value={timeRange}
            onChange={(e) => handleTimeRangeChange(e.target.value)}
            className="form-select text-sm"
          >
            <option value="7d">最近7天</option>
            <option value="30d">最近30天</option>
            <option value="90d">最近90天</option>
            <option value="1y">最近一年</option>
          </select>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="总收入"
          value={12560}
          change={{ value: 12, type: 'increase' }}
          icon={<DollarSign />}
          color="green"
        />
        <StatsCard
          title="广告展示"
          value={245000}
          change={{ value: 8, type: 'increase' }}
          icon={<Eye />}
          color="blue"
        />
        <StatsCard
          title="平均点击率"
          value={3.2}
          change={{ value: 0.5, type: 'increase' }}
          icon={<TrendingUp />}
          color="yellow"
        />
        <StatsCard
          title="付费用户"
          value={1240}
          change={{ value: 15, type: 'increase' }}
          icon={<Users />}
          color="red"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 广告位管理 */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">广告位管理</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">广告位名称</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">收益</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {adPlacements.map((placement) => (
                  <tr key={placement.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{placement.name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{placement.type}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        placement.status === '活跃' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {placement.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥{placement.revenue.toLocaleString()}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <button className="text-blue-600 hover:text-blue-900 mr-3">编辑</button>
                      <button className="text-red-600 hover:text-red-900">删除</button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="bg-gray-50 px-6 py-3 flex items-center justify-between border-t border-gray-200">
            <div className="text-sm text-gray-700">
              显示 <span className="font-medium">1</span> 到 <span className="font-medium">4</span> 条，共 <span className="font-medium">4</span> 条记录
            </div>
            <div className="flex space-x-2">
              <button className="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 bg-white hover:bg-gray-50">
                上一页
              </button>
              <button className="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 bg-white hover:bg-gray-50">
                下一页
              </button>
            </div>
          </div>
        </div>

        {/* 提现管理 */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">提现管理</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金额</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">日期</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {withdrawalRecords.map((record) => (
                  <tr key={record.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{record.user}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥{record.amount}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        record.status === '已完成' 
                          ? 'bg-green-100 text-green-800' 
                          : record.status === '处理中' 
                            ? 'bg-yellow-100 text-yellow-800' 
                            : 'bg-red-100 text-red-800'
                      }`}>
                        {record.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{record.date}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {record.status === '处理中' && (
                        <>
                          <button 
                            onClick={() => handleApproveWithdrawal(record.id)}
                            className="text-green-600 hover:text-green-900 mr-3"
                          >
                            批准
                          </button>
                          <button 
                            onClick={() => handleRejectWithdrawal(record.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            拒绝
                          </button>
                        </>
                      )}
                      {record.status === '已完成' && (
                        <button className="text-blue-600 hover:text-blue-900">
                          详情
                        </button>
                      )}
                      {record.status === '已拒绝' && (
                        <button className="text-blue-600 hover:text-blue-900">
                          详情
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="bg-gray-50 px-6 py-3 flex items-center justify-between border-t border-gray-200">
            <div className="text-sm text-gray-700">
              显示 <span className="font-medium">1</span> 到 <span className="font-medium">4</span> 条，共 <span className="font-medium">4</span> 条记录
            </div>
            <div className="flex space-x-2">
              <button className="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 bg-white hover:bg-gray-50">
                上一页
              </button>
              <button className="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 bg-white hover:bg-gray-50">
                下一页
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 收益趋势图表 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">收益趋势</h3>
        <div className="h-64 flex items-center justify-center text-gray-500">
          收益趋势图表待实现
        </div>
      </div>
    </div>
  );
};

export default MonetizationPage;