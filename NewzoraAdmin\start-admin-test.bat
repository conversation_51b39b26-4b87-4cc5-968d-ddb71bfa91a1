@echo off
echo ========================================
echo    Newzora Admin 后台管理系统测试启动
echo ========================================
echo.

echo [1/3] 检查环境配置...
if not exist "Backend\.env" (
    echo ❌ 后端环境配置文件不存在
    pause
    exit /b 1
)

echo [2/3] 启动后端服务...
cd Backend
start "Newzora Admin Backend" cmd /k "npm run dev"
timeout /t 3 /nobreak >nul

echo [3/3] 启动前端服务...
cd ..\Frontend
start "Newzora Admin Frontend" cmd /k "npm run dev"
timeout /t 3 /nobreak >nul

echo.
echo ✅ 服务启动完成！
echo.
echo 📍 访问地址:
echo    - 测试页面: http://localhost:3001/test
echo    - 管理登录: http://localhost:3001/admin/login
echo    - 仪表板:   http://localhost:3001/admin/dashboard
echo    - API健康检查: http://localhost:5001/health
echo.
echo 🔑 测试账户:
echo    - 超级管理员: <EMAIL> / admin123456
echo    - 管理员: <EMAIL> / test123456
echo    - 审核员: <EMAIL> / mod123456
echo.

timeout /t 5 /nobreak >nul
start http://localhost:3001/test

echo 按任意键退出...
pause >nul