
'use client';

import React, { useState } from 'react';
import { Calendar, TrendingUp, Users, UserPlus, Activity } from 'lucide-react';

const UserAnalyticsPage: React.FC = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [isLoading, setIsLoading] = useState(false);

  // Mock data for user analytics
  const userGrowthData = [
    { date: '2023-06-01', total: 1200, new: 45, active: 980 },
    { date: '2023-06-02', total: 1250, new: 50, active: 1020 },
    { date: '2023-06-03', total: 1305, new: 55, active: 1070 },
    { date: '2023-06-04', total: 1360, new: 55, active: 1120 },
    { date: '2023-06-05', total: 1420, new: 60, active: 1180 },
    { date: '2023-06-06', total: 1480, new: 60, active: 1230 },
    { date: '2023-06-07', total: 1540, new: 60, active: 1280 },
  ];

  const userActivityData = [
    { time: '00:00', count: 120 },
    { time: '04:00', count: 80 },
    { time: '08:00', count: 320 },
    { time: '12:00', count: 580 },
    { time: '16:00', count: 640 },
    { time: '20:00', count: 420 },
    { time: '24:00', count: 180 },
  ];

  const userDemographicsData = [
    { age: '18-24', percentage: 25 },
    { age: '25-34', percentage: 35 },
    { age: '35-44', percentage: 20 },
    { age: '45-54', percentage: 12 },
    { age: '55+', percentage: 8 },
  ];

  const handleTimeRangeChange = (range: string) => {
    setTimeRange(range);
    setIsLoading(true);
    // In a real app, this would fetch new data
    setTimeout(() => setIsLoading(false), 500);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">User Analytics</h1>
        <div className="flex items-center space-x-2">
          <select
            value={timeRange}
            onChange={(e) => handleTimeRangeChange(e.target.value)}
            className="form-select"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 text-blue-600">
              <Users className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <h2 className="text-sm font-medium text-gray-500">Total Users</h2>
              <p className="text-2xl font-semibold text-gray-900">1,540</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 text-green-600">
              <UserPlus className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <h2 className="text-sm font-medium text-gray-500">New Users</h2>
              <p className="text-2xl font-semibold text-gray-900">385</p>
              <p className="text-xs text-green-600">+12.5% from last period</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 text-purple-600">
              <Activity className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <h2 className="text-sm font-medium text-gray-500">Active Users</h2>
              <p className="text-2xl font-semibold text-gray-900">1,280</p>
              <p className="text-xs text-green-600">+8.3% from last period</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
              <TrendingUp className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <h2 className="text-sm font-medium text-gray-500">Retention Rate</h2>
              <p className="text-2xl font-semibold text-gray-900">83.1%</p>
              <p className="text-xs text-green-600">+2.4% from last period</p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Growth Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium text-gray-900">User Growth</h2>
            <div className="flex items-center text-sm text-gray-500">
              <Calendar className="h-4 w-4 mr-1" />
              {timeRange === '7d' ? 'Last 7 days' : 
               timeRange === '30d' ? 'Last 30 days' : 
               timeRange === '90d' ? 'Last 90 days' : 'Last year'}
            </div>
          </div>

          {isLoading ? (
            <div className="h-64 flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : (
            <div className="h-64 flex items-end space-x-2">
              {userGrowthData.map((data, index) => (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div 
                    className="w-full bg-blue-500 rounded-t hover:bg-blue-600 transition-colors"
                    style={{ height: `${(data.total / 1600) * 100}%` }}
                  ></div>
                  <div className="text-xs text-gray-500 mt-1">
                    {data.date.split('-').slice(1).join('/')}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* User Activity Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">User Activity by Time of Day</h2>

          {isLoading ? (
            <div className="h-64 flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : (
            <div className="h-64 flex items-end space-x-2">
              {userActivityData.map((data, index) => (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div 
                    className="w-full bg-green-500 rounded-t hover:bg-green-600 transition-colors"
                    style={{ height: `${(data.count / 700) * 100}%` }}
                  ></div>
                  <div className="text-xs text-gray-500 mt-1">
                    {data.time}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* User Demographics */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">User Demographics</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Age Distribution */}
          <div>
            <h3 className="text-md font-medium text-gray-700 mb-3">Age Distribution</h3>
            <div className="space-y-3">
              {userDemographicsData.map((data, index) => (
                <div key={index}>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm text-gray-600">{data.age}</span>
                    <span className="text-sm font-medium text-gray-900">{data.percentage}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${data.percentage}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* User Engagement */}
          <div>
            <h3 className="text-md font-medium text-gray-700 mb-3">User Engagement</h3>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-600">Daily Active Users</span>
                  <span className="text-sm font-medium text-gray-900">1,280</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full" style={{ width: '83%' }}></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-600">Weekly Active Users</span>
                  <span className="text-sm font-medium text-gray-900">1,420</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full" style={{ width: '92%' }}></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-600">Monthly Active Users</span>
                  <span className="text-sm font-medium text-gray-900">1,490</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-purple-600 h-2 rounded-full" style={{ width: '97%' }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserAnalyticsPage;
