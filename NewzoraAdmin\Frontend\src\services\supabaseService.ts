import { supabase } from '@/lib/supabase';
import { DashboardStats } from '@/types/admin';

export const supabaseService = {
  // 仪表板数据 - 使用模拟数据
  async getDashboardStats(): Promise<DashboardStats> {
    return {
      users: {
        total: 156,
        todayNew: 12,
        activeUsers: 89
      },
      content: {
        totalArticles: 234,
        todayPublished: 8,
        pendingReview: 5
      },
      engagement: {
        totalComments: 567,
        todayComments: 23,
        averageReadTime: 5
      }
    };
  },

  // 用户管理 - 使用模拟数据
  async getUsers(params: any = {}) {
    const mockUsers = [
      {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        display_name: '超级管理员',
        role: 'super_admin',
        is_active: true,
        email_verified: true,
        created_at: '2024-01-01',
        last_login_at: '2024-12-20'
      },
      {
        id: '2',
        username: 'user1',
        email: '<EMAIL>',
        display_name: '测试用户1',
        role: 'user',
        is_active: true,
        email_verified: true,
        created_at: '2024-01-15',
        last_login_at: '2024-12-19'
      }
    ];

    return {
      users: mockUsers,
      pagination: {
        current: 1,
        total: mockUsers.length,
        pageSize: 20,
        totalPages: 1
      }
    };
  },

  async updateUserStatus(userId: string, isActive: boolean) {
    console.log('更新用户状态:', userId, isActive);
    return { success: true };
  },

  async updateUserRole(userId: string, role: string) {
    console.log('更新用户角色:', userId, role);
    return { success: true };
  },

  async bulkUpdateUsers(userIds: string[], action: string, value?: any) {
    console.log('批量更新用户:', userIds, action, value);
    return { updated: userIds.length };
  },

  // 文章管理 - 使用模拟数据
  async getArticles(params: any = {}) {
    const mockArticles = [
      {
        id: '1',
        title: 'React 18 新特性详解',
        content: '这是一篇关于React 18的文章...',
        author_id: '1',
        category: '技术',
        published: true,
        views: 1234,
        likes: 89,
        created_at: '2024-12-01',
        profiles: {
          username: 'admin',
          display_name: '超级管理员'
        }
      },
      {
        id: '2',
        title: 'TypeScript 最佳实践',
        content: '这是一篇关于TypeScript的文章...',
        author_id: '2',
        category: '技术',
        published: false,
        views: 567,
        likes: 34,
        created_at: '2024-12-02',
        profiles: {
          username: 'user1',
          display_name: '测试用户1'
        }
      }
    ];

    return {
      articles: mockArticles,
      pagination: {
        current: 1,
        total: mockArticles.length,
        pageSize: 20,
        totalPages: 1
      }
    };
  },

  async updateArticleStatus(articleId: string, published: boolean) {
    console.log('更新文章状态:', articleId, published);
    return { success: true };
  }
};