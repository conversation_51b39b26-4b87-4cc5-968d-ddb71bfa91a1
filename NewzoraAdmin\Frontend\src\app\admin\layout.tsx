'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import AdminLayout from '@/components/admin/layout/AdminLayout';

export default function AdminLayoutPage({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  
  // 登录页面不使用 AdminLayout
  if (pathname === '/admin/login') {
    return <>{children}</>;
  }
  
  return <AdminLayout>{children}</AdminLayout>;
}