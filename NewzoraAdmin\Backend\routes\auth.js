const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const router = express.Router();

// 测试账户数据
const testAccounts = [
  {
    id: '1',
    email: '<EMAIL>',
    password: '$2a$10$JjzCTaBPupA7lR3j2VFvZONtkq3KMgW80c.7px9v94hgBOcR4jadm',
    name: '超级管理员',
    role: 'super_admin'
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: '$2a$10$S8cFG8gjozWy82uENisy6.2IvjOfPwE.O52jh7bDdLwAFl/K2Q4zW',
    name: '测试管理员',
    role: 'admin'
  },
  {
    id: '3',
    email: '<EMAIL>',
    password: '$2a$10$a.AOhxSfqLpOHnVlaPpuPO.1khvxTkIDkEPxcIxIaLAs7L4uDp.MC',
    name: '审核员',
    role: 'moderator'
  }
];

// 管理员登录
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: '邮箱和密码不能为空'
      });
    }

    // 查找测试账户
    const admin = testAccounts.find(acc => acc.email === email);
    if (!admin) {
      return res.status(401).json({
        success: false,
        message: '邮箱或密码错误'
      });
    }

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, admin.password);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: '邮箱或密码错误'
      });
    }

    // 生成JWT令牌
    const token = jwt.sign(
      { 
        id: admin.id, 
        email: admin.email, 
        role: admin.role 
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );

    res.json({
      success: true,
      message: '登录成功',
      token,
      user: {
        id: admin.id,
        email: admin.email,
        name: admin.name,
        role: admin.role
      }
    });
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

module.exports = router;