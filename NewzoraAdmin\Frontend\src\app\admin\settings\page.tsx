'use client';

import React, { useState } from 'react';
import { Save, Shield, Mail, Globe, Database } from 'lucide-react';

const SettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('basic');
  const [loading, setLoading] = useState(false);

  const tabs = [
    { id: 'basic', name: '基础设置', icon: Globe },
    { id: 'security', name: '安全设置', icon: Shield },
    { id: 'email', name: '邮件设置', icon: Mail },
    { id: 'database', name: '数据库设置', icon: Database }
  ];

  const handleSave = async () => {
    setLoading(true);
    // 模拟保存操作
    setTimeout(() => {
      setLoading(false);
      alert('设置已保存');
    }, 1000);
  };

  const renderBasicSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          网站名称
        </label>
        <input
          type="text"
          defaultValue="Newzora"
          className="form-input"
          placeholder="请输入网站名称"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          网站描述
        </label>
        <textarea
          rows={3}
          defaultValue="现代化内容管理平台"
          className="form-input"
          placeholder="请输入网站描述"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          网站Logo URL
        </label>
        <input
          type="url"
          className="form-input"
          placeholder="https://example.com/logo.png"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          联系邮箱
        </label>
        <input
          type="email"
          defaultValue="<EMAIL>"
          className="form-input"
          placeholder="<EMAIL>"
        />
      </div>
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          最大登录尝试次数
        </label>
        <input
          type="number"
          defaultValue="5"
          className="form-input"
          min="1"
          max="10"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          会话超时时间（分钟）
        </label>
        <input
          type="number"
          defaultValue="60"
          className="form-input"
          min="5"
          max="1440"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          密码最小长度
        </label>
        <input
          type="number"
          defaultValue="8"
          className="form-input"
          min="6"
          max="20"
        />
      </div>
      
      <div className="flex items-center">
        <input
          type="checkbox"
          id="require-2fa"
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <label htmlFor="require-2fa" className="ml-2 text-sm text-gray-700">
          启用双因素认证
        </label>
      </div>
      
      <div className="flex items-center">
        <input
          type="checkbox"
          id="force-https"
          defaultChecked
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <label htmlFor="force-https" className="ml-2 text-sm text-gray-700">
          强制使用HTTPS
        </label>
      </div>
    </div>
  );

  const renderEmailSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          SMTP服务器
        </label>
        <input
          type="text"
          className="form-input"
          placeholder="smtp.gmail.com"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          SMTP端口
        </label>
        <input
          type="number"
          defaultValue="587"
          className="form-input"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          发件人邮箱
        </label>
        <input
          type="email"
          className="form-input"
          placeholder="<EMAIL>"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          发件人名称
        </label>
        <input
          type="text"
          defaultValue="Newzora"
          className="form-input"
        />
      </div>
      
      <div className="flex items-center">
        <input
          type="checkbox"
          id="enable-ssl"
          defaultChecked
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <label htmlFor="enable-ssl" className="ml-2 text-sm text-gray-700">
          启用SSL/TLS加密
        </label>
      </div>
    </div>
  );

  const renderDatabaseSettings = () => (
    <div className="space-y-6">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              注意
            </h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>数据库设置修改需要重启服务才能生效，请谨慎操作。</p>
            </div>
          </div>
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          数据库连接池大小
        </label>
        <input
          type="number"
          defaultValue="10"
          className="form-input"
          min="1"
          max="50"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          查询超时时间（秒）
        </label>
        <input
          type="number"
          defaultValue="30"
          className="form-input"
          min="5"
          max="300"
        />
      </div>
      
      <div className="flex items-center">
        <input
          type="checkbox"
          id="enable-query-log"
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <label htmlFor="enable-query-log" className="ml-2 text-sm text-gray-700">
          启用查询日志
        </label>
      </div>
      
      <div className="flex items-center">
        <input
          type="checkbox"
          id="auto-backup"
          defaultChecked
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <label htmlFor="auto-backup" className="ml-2 text-sm text-gray-700">
          启用自动备份
        </label>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'basic':
        return renderBasicSettings();
      case 'security':
        return renderSecuritySettings();
      case 'email':
        return renderEmailSettings();
      case 'database':
        return renderDatabaseSettings();
      default:
        return renderBasicSettings();
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">系统设置</h1>
        <button
          onClick={handleSave}
          disabled={loading}
          className="btn-primary flex items-center"
        >
          {loading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          ) : (
            <Save className="w-4 h-4 mr-2" />
          )}
          保存设置
        </button>
      </div>

      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4 mr-2" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;