'use client';

import React, { useState, useEffect } from 'react';
import { Users, FileText, MessageCircle, Activity } from 'lucide-react';
import StatsCard from '@/components/admin/dashboard/StatsCard';
import TrendChart from '@/components/admin/dashboard/TrendChart';
import QuickActions from '@/components/admin/dashboard/QuickActions';
import RecentActivities from '@/components/admin/dashboard/RecentActivities';
import { dashboardService } from '@/services/dashboardService';
import { DashboardStats } from '@/types/admin';

const DashboardPage: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');
  
  // User growth trend data
  const [userGrowthData, setUserGrowthData] = useState([
    { name: '<PERSON>', users: 400 },
    { name: '<PERSON><PERSON>', users: 600 },
    { name: 'Wed', users: 300 },
    { name: 'Thu', users: 800 },
    { name: 'Fri', users: 500 },
    { name: 'Sat', users: 900 },
    { name: 'Sun', users: 400 },
  ]);
  
  // Content publishing trend data
  const [contentPublishData, setContentPublishData] = useState([
    { name: 'Mon', articles: 12 },
    { name: 'Tue', articles: 19 },
    { name: 'Wed', articles: 8 },
    { name: 'Thu', articles: 15 },
    { name: 'Fri', articles: 12 },
    { name: 'Sat', articles: 20 },
    { name: 'Sun', articles: 18 },
  ]);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const data = await dashboardService.getStats();
        setStats(data);
      } catch (error) {
        console.error('Failed to fetch statistics:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const handleTimeRangeChange = (range: string) => {
    setTimeRange(range);
    // Generate different sample data based on time range
    switch (range) {
      case '30d':
        setUserGrowthData([
          { name: 'Week 1', users: 2100 },
          { name: 'Week 2', users: 2400 },
          { name: 'Week 3', users: 1900 },
          { name: 'Week 4', users: 2800 },
        ]);
        
        setContentPublishData([
          { name: 'Week 1', articles: 65 },
          { name: 'Week 2', articles: 78 },
          { name: 'Week 3', articles: 52 },
          { name: 'Week 4', articles: 95 },
        ]);
        break;
      case '90d':
        setUserGrowthData([
          { name: 'Month 1', users: 8200 },
          { name: 'Month 2', users: 9500 },
          { name: 'Month 3', users: 11200 },
        ]);
        
        setContentPublishData([
          { name: 'Month 1', articles: 240 },
          { name: 'Month 2', articles: 280 },
          { name: 'Month 3', articles: 320 },
        ]);
        break;
      default: // 7d
        setUserGrowthData([
          { name: 'Mon', users: 400 },
          { name: 'Tue', users: 600 },
          { name: 'Wed', users: 300 },
          { name: 'Thu', users: 800 },
          { name: 'Fri', users: 500 },
          { name: 'Sat', users: 900 },
          { name: 'Sun', users: 400 },
        ]);
        
        setContentPublishData([
          { name: 'Mon', articles: 12 },
          { name: 'Tue', articles: 19 },
          { name: 'Wed', articles: 8 },
          { name: 'Thu', articles: 15 },
          { name: 'Fri', articles: 12 },
          { name: 'Sat', articles: 20 },
          { name: 'Sun', articles: 18 },
        ]);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-4"></div>
              <div className="h-8 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <div className="flex items-center space-x-2">
          <select 
            value={timeRange}
            onChange={(e) => handleTimeRangeChange(e.target.value)}
            className="form-select text-sm"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Users"
          value={stats?.users.total || 0}
          change={{ value: 12, type: 'increase' }}
          icon={<Users />}
          color="blue"
          onClick={() => router.push('/admin/users')}
        />
        <StatsCard
          title="Total Articles"
          value={stats?.content.totalArticles || 0}
          change={{ value: 8, type: 'increase' }}
          icon={<FileText />}
          color="green"
          onClick={() => router.push('/admin/content')}
        />
        <StatsCard
          title="Total Comments"
          value={stats?.engagement.totalComments || 0}
          change={{ value: 5, type: 'decrease' }}
          icon={<MessageCircle />}
          color="yellow"
        />
        <StatsCard
          title="Active Users"
          value={stats?.users.activeUsers || 0}
          change={{ value: 15, type: 'increase' }}
          icon={<Activity />}
          color="red"
        />
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TrendChart 
          title="User Growth Trend" 
          data={userGrowthData} 
          dataKey="users" 
          color="#3b82f6" 
        />
        <TrendChart 
          title="Content Publishing Trend" 
          data={contentPublishData} 
          dataKey="articles" 
          color="#10b981" 
        />
      </div>

      {/* 快速操作和最新活动 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <QuickActions />
        
        <RecentActivities />
      </div>
    </div>
  );
};

export default DashboardPage;