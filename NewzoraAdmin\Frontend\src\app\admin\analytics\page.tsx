'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';
import { Users, FileText, MessageCircle, TrendingUp } from 'lucide-react';
import StatsCard from '@/components/admin/dashboard/StatsCard';
import { supabaseService } from '@/services/supabaseService';

const AnalyticsPage: React.FC = () => {
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');

  // 模拟数据
  const userGrowthData = [
    { date: '2024-12-01', users: 120 },
    { date: '2024-12-02', users: 132 },
    { date: '2024-12-03', users: 145 },
    { date: '2024-12-04', users: 158 },
    { date: '2024-12-05', users: 167 },
    { date: '2024-12-06', users: 178 },
    { date: '2024-12-07', users: 189 }
  ];

  const contentData = [
    { category: '技术', count: 45 },
    { category: '生活', count: 32 },
    { category: '随笔', count: 28 },
    { category: '教程', count: 21 },
    { category: '其他', count: 15 }
  ];

  const activityData = [
    { time: '00:00', users: 12 },
    { time: '04:00', users: 8 },
    { time: '08:00', users: 45 },
    { time: '12:00', users: 78 },
    { time: '16:00', users: 65 },
    { time: '20:00', users: 89 },
    { time: '23:59', users: 34 }
  ];

  const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const data = await supabaseService.getDashboardStats();
        setStats(data);
      } catch (error) {
        console.error('获取统计数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">数据分析</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-4"></div>
              <div className="h-8 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">数据分析</h1>
        <div className="flex items-center space-x-2">
          <select 
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="form-select text-sm"
          >
            <option value="7d">最近7天</option>
            <option value="30d">最近30天</option>
            <option value="90d">最近90天</option>
          </select>
          <button className="btn-secondary text-sm">导出报告</button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="总用户数"
          value={stats?.users.total || 0}
          change={{ value: 12, type: 'increase' }}
          icon={<Users />}
          color="blue"
        />
        <StatsCard
          title="总文章数"
          value={stats?.content.totalArticles || 0}
          change={{ value: 8, type: 'increase' }}
          icon={<FileText />}
          color="green"
        />
        <StatsCard
          title="总评论数"
          value={stats?.engagement.totalComments || 0}
          change={{ value: 5, type: 'decrease' }}
          icon={<MessageCircle />}
          color="yellow"
        />
        <StatsCard
          title="活跃用户"
          value={stats?.users.activeUsers || 0}
          change={{ value: 15, type: 'increase' }}
          icon={<TrendingUp />}
          color="red"
        />
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 用户增长趋势 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">用户增长趋势</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={userGrowthData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="users" stroke="#3b82f6" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* 内容分类分布 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">内容分类分布</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={contentData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="count"
              >
                {contentData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* 更多图表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 用户活跃时间分布 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">用户活跃时间分布</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={activityData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="time" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="users" fill="#10b981" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* 热门内容排行 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">热门内容排行</h3>
          <div className="space-y-4">
            {[
              { title: 'React 18 新特性详解', views: 1234, likes: 89 },
              { title: 'TypeScript 最佳实践', views: 987, likes: 76 },
              { title: 'Next.js 14 升级指南', views: 856, likes: 65 },
              { title: 'Tailwind CSS 技巧分享', views: 743, likes: 54 },
              { title: 'JavaScript 性能优化', views: 621, likes: 43 }
            ].map((item, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <span className="w-6 h-6 bg-blue-600 text-white text-xs rounded-full flex items-center justify-center mr-3">
                    {index + 1}
                  </span>
                  <span className="font-medium text-gray-900">{item.title}</span>
                </div>
                <div className="text-sm text-gray-500">
                  {item.views} 浏览 · {item.likes} 点赞
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsPage;