'use client';

import React, { useState } from 'react';
import { Search, Filter, Eye, Trash2 } from 'lucide-react';

interface Report {
  id: string;
  reporter: string;
  targetType: 'article' | 'comment' | 'user';
  targetTitle: string;
  reason: string;
  status: 'pending' | 'resolved' | 'dismissed';
  createdAt: string;
  reporterId: string;
  targetId: string;
}

const ReportsPage: React.FC = () => {
  const [reports, setReports] = useState<Report[]>([
    {
      id: '1',
      reporter: 'User A',
      targetType: 'article',
      targetTitle: '如何学习React',
      reason: 'Content Plagiarism',
      status: 'pending',
      createdAt: '2024-06-01 10:30:00',
      reporterId: 'user1',
      targetId: 'article1'
    },
    {
      id: '2',
      reporter: 'User B',
      targetType: 'comment',
      targetTitle: '这篇文章很有帮助',
      reason: 'Malicious Speech',
      status: 'pending',
      createdAt: '2024-06-02 14:15:00',
      reporterId: 'user2',
      targetId: 'comment1'
    },
    {
      id: '3',
      reporter: 'User C',
      targetType: 'user',
      targetTitle: 'User D',
      reason: 'Avatar Violation',
      status: 'resolved',
      createdAt: '2024-05-28 09:45:00',
      reporterId: 'user3',
      targetId: 'user4'
    },
    {
      id: '4',
      reporter: 'User E',
      targetType: 'article',
      targetTitle: 'JavaScript高级技巧',
      reason: 'False Information',
      status: 'dismissed',
      createdAt: '2024-05-25 16:20:00',
      reporterId: 'user5',
      targetId: 'article2'
    }
  ]);
  
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedReports, setSelectedReports] = useState<string[]>([]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // 在实际应用中，这里应该重新获取数据
  };

  const handleResolveReport = (id: string) => {
    setReports(prev => prev.map(report => 
      report.id === id ? { ...report, status: 'resolved' } : report
    ));
    alert('Report has been resolved');
  };

  const handleDismissReport = (id: string) => {
    setReports(prev => prev.map(report => 
      report.id === id ? { ...report, status: 'dismissed' } : report
    ));
    alert('Report has been dismissed');
  };

  const handleDeleteReport = (id: string) => {
    if (confirm('Are you sure you want to delete this report record?')) {
      setReports(prev => prev.filter(report => report.id !== id));
      setSelectedReports(prev => prev.filter(reportId => reportId !== id));
      alert('Report record has been deleted');
    }
  };

  const filteredReports = reports.filter(report => {
    const matchesSearch = report.targetTitle.toLowerCase().includes(searchQuery.toLowerCase()) || 
                          report.reporter.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = typeFilter ? report.targetType === typeFilter : true;
    const matchesStatus = statusFilter ? report.status === statusFilter : true;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  const handleBulkAction = (action: string) => {
    if (selectedReports.length === 0) {
      alert('Please select report records first');
      return;
    }
    
    if (!confirm(`Are you sure you want to ${action} ${selectedReports.length} report records?`)) {
      return;
    }
    
    switch (action) {
      case 'resolve':
        setReports(prev => prev.map(report => 
          selectedReports.includes(report.id) ? { ...report, status: 'resolved' } : report
        ));
        break;
      case 'dismiss':
        setReports(prev => prev.map(report => 
          selectedReports.includes(report.id) ? { ...report, status: 'dismissed' } : report
        ));
        break;
      case 'delete':
        setReports(prev => prev.filter(report => !selectedReports.includes(report.id)));
        setSelectedReports([]);
        break;
    }
    
    alert(`Batch operation successful`);
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Report Management</h1>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSearch} className="flex flex-wrap gap-4">
          <div className="flex-1 min-w-64">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search report content or reporter..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="form-input pl-10"
              />
            </div>
          </div>
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="form-select w-32"
          >
            <option value="">All Types</option>
            <option value="article">Article</option>
            <option value="comment">Comment</option>
            <option value="user">User</option>
          </select>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="form-select w-32"
          >
            <option value="">All Status</option>
            <option value="pending">Pending</option>
            <option value="resolved">Resolved</option>
            <option value="dismissed">Dismissed</option>
          </select>
          <button type="submit" className="btn-primary flex items-center">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </button>
        </form>
      </div>

      {/* 批量操作 */}
      {selectedReports.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-800">
              Selected {selectedReports.length} report records
            </span>
            <div className="flex space-x-2">
              <button
                onClick={() => handleBulkAction('resolve')}
                className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
              >
                Bulk Resolve
              </button>
              <button
                onClick={() => handleBulkAction('dismiss')}
                className="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700"
              >
                Bulk Dismiss
              </button>
              <button
                onClick={() => handleBulkAction('delete')}
                className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
              >
                Bulk Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 举报列表 */}
      <div className="bg-white rounded-lg shadow">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300"
                    checked={selectedReports.length === filteredReports.length && filteredReports.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedReports(filteredReports.map(report => report.id));
                      } else {
                        setSelectedReports([]);
                      }
                    }}
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Report Info</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Report Type</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Report Reason</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Report Time</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredReports.map((report) => (
                <tr key={report.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300"
                      checked={selectedReports.includes(report.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedReports(prev => [...prev, report.id]);
                        } else {
                          setSelectedReports(prev => prev.filter(id => id !== report.id));
                        }
                      }}
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{report.targetTitle}</div>
                    <div className="text-sm text-gray-500">Reporter: {report.reporter}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                      {report.targetType === 'article' && 'Article'}
                      {report.targetType === 'comment' && 'Comment'}
                      {report.targetType === 'user' && 'User'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {report.reason}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      report.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      report.status === 'resolved' ? 'bg-green-100 text-green-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {report.status === 'pending' && 'Pending'}
                      {report.status === 'resolved' && 'Resolved'}
                      {report.status === 'dismissed' && 'Dismissed'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {report.createdAt}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex space-x-2">
                      <button 
                        onClick={() => alert(`View ${report.targetType}: ${report.targetTitle}`)}
                        className="text-blue-600 hover:text-blue-900"
                        title="View"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      {report.status === 'pending' && (
                        <>
                          <button 
                            onClick={() => handleResolveReport(report.id)}
                            className="text-green-600 hover:text-green-900"
                            title="Resolve"
                          >
                            Resolve
                          </button>
                          <button 
                            onClick={() => handleDismissReport(report.id)}
                            className="text-yellow-600 hover:text-yellow-900"
                            title="Dismiss"
                          >
                            Dismiss
                          </button>
                        </>
                      )}
                      <button 
                        onClick={() => handleDeleteReport(report.id)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className="bg-gray-50 px-6 py-3 flex items-center justify-between border-t border-gray-200">
          <div className="text-sm text-gray-700">
            Showing <span className="font-medium">1</span> to <span className="font-medium">{filteredReports.length}</span> of <span className="font-medium">{filteredReports.length}</span> records
          </div>
          <div className="flex space-x-2">
            <button className="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 bg-white hover:bg-gray-50">
              Previous
            </button>
            <button className="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 bg-white hover:bg-gray-50">
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportsPage;