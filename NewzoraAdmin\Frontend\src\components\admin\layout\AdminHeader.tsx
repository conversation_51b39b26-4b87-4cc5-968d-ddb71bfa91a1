'use client';

import React, { useEffect, useState, useRef } from 'react';
import { Search, User, LogOut, X } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import NotificationBell from './NotificationBell';
import LanguageSwitcher from '@/components/admin/common/LanguageSwitcher';

const AdminHeader: React.FC = () => {
  const { user, logout } = useAuth();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchPlaceholder, setSearchPlaceholder] = useState('Search users, articles, comments...');
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const handleLanguageChange = (e: CustomEvent) => {
      const language = e.detail;
      if (language === 'en') {
        setSearchPlaceholder('Search users, articles, comments...');
      } else {
        setSearchPlaceholder('Search users, articles, comments...');
      }
    };

    // 从localStorage加载搜索历史
    const savedHistory = localStorage.getItem('admin_search_history');
    if (savedHistory) {
      setSearchHistory(JSON.parse(savedHistory));
    }

    window.addEventListener('languageChange', handleLanguageChange as EventListener);
    return () => {
      window.removeEventListener('languageChange', handleLanguageChange as EventListener);
    };
  }, []);

  const handleLogout = async () => {
    await logout();
    router.push('/admin/login');
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (searchQuery.trim()) {
      // 添加到搜索历史
      const updatedHistory = [
        searchQuery,
        ...searchHistory.filter(item => item !== searchQuery)
      ].slice(0, 5); // 最多保存5条历史记录
      
      setSearchHistory(updatedHistory);
      localStorage.setItem('admin_search_history', JSON.stringify(updatedHistory));
      
      // 执行搜索 - 跳转到搜索结果页面
      router.push(`/admin/search?q=${encodeURIComponent(searchQuery)}`);
      
      // 清空搜索框
      setSearchQuery('');
      setIsSearchFocused(false);
    }
  };

  const clearSearchHistory = () => {
    setSearchHistory([]);
    localStorage.removeItem('admin_search_history');
  };

  const removeFromHistory = (item: string, e: React.MouseEvent) => {
    e.stopPropagation();
    const updatedHistory = searchHistory.filter(historyItem => historyItem !== item);
    setSearchHistory(updatedHistory);
    localStorage.setItem('admin_search_history', JSON.stringify(updatedHistory));
  };

  return (
    <header className="h-16 bg-white border-b border-gray-200 px-6 flex items-center justify-between fixed top-0 left-0 right-0 z-50">
      {/* Logo区域 */}
      <div className="flex items-center">
        <h1 className="text-xl font-bold text-gray-900" data-translate="Newzora Admin">Newzora Admin</h1>
      </div>

      {/* 搜索框 */}
      <div className="flex-1 max-w-md mx-8 relative">
        <form onSubmit={handleSearch}>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              ref={searchInputRef}
              type="text"
              placeholder={searchPlaceholder}
              data-translate-placeholder="Search users, articles, comments..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onFocus={() => setIsSearchFocused(true)}
              onBlur={() => {
                // 延迟隐藏搜索建议，确保点击建议时不会立即隐藏
                setTimeout(() => setIsSearchFocused(false), 200);
              }}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            {searchQuery && (
              <button
                type="button"
                onClick={() => setSearchQuery('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
        </form>
        
        {/* 搜索建议和历史记录 */}
        {isSearchFocused && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
            {searchQuery ? (
              // 搜索建议
              <div className="py-2">
                <div 
                  className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer flex items-center"
                  onClick={() => {
                    setSearchQuery(searchQuery);
                    handleSearch({ preventDefault: () => {} } as React.FormEvent);
                  }}
                >
                  <Search className="w-4 h-4 mr-2 text-gray-400" />
                  Search "{searchQuery}"
                </div>
              </div>
            ) : (
              // 搜索历史
              searchHistory.length > 0 && (
                <div className="py-2">
                  <div className="px-4 py-2 flex items-center justify-between">
                    <span className="text-xs font-medium text-gray-500 uppercase">Search History</span>
                    <button 
                      onClick={clearSearchHistory}
                      className="text-xs text-blue-600 hover:text-blue-800"
                    >
                      Clear History
                    </button>
                  </div>
                  {searchHistory.map((item, index) => (
                    <div 
                      key={index}
                      className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer flex items-center justify-between"
                      onClick={() => {
                        setSearchQuery(item);
                        searchInputRef.current?.focus();
                      }}
                    >
                      <div className="flex items-center">
                        <Search className="w-4 h-4 mr-2 text-gray-400" />
                        {item}
                      </div>
                      <button 
                        onClick={(e) => removeFromHistory(item, e)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )
            )}
          </div>
        )}
      </div>

      {/* 功能区域 */}
      <div className="flex items-center space-x-4">
        <NotificationBell />
        
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
            <User className="w-4 h-4 text-white" />
          </div>
          <span className="text-sm font-medium text-gray-700">{user?.display_name}</span>
        </div>

        <button
          onClick={handleLogout}
          className="p-2 text-gray-400 hover:text-gray-600"
          title="Logout"
          data-translate="Logout"
        >
          <LogOut className="w-5 h-5" />
        </button>
      </div>
    </header>
  );
};

export default AdminHeader;