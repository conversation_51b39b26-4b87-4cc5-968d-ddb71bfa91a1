import { supabaseService } from './supabaseService';
import { DashboardStats } from '@/types/admin';

export const dashboardService = {
  // 获取仪表板统计数据
  async getStats(): Promise<DashboardStats> {
    try {
      return await supabaseService.getDashboardStats();
    } catch (error) {
      console.error('获取仪表板统计失败:', error);
      throw error;
    }
  },

  // 获取用户增长趋势
  async getUserGrowthTrend(days: number = 30) {
    try {
      const response = await api.get(`/api/admin/dashboard/user-growth?days=${days}`);
      return response.data.data;
    } catch (error) {
      console.error('获取用户增长趋势失败:', error);
      throw error;
    }
  },

  // 获取内容发布趋势
  async getContentTrend(days: number = 30) {
    try {
      const response = await api.get(`/api/admin/dashboard/content-trend?days=${days}`);
      return response.data.data;
    } catch (error) {
      console.error('获取内容趋势失败:', error);
      throw error;
    }
  },

  // 获取分类统计
  async getCategoryStats() {
    try {
      const response = await api.get('/api/admin/dashboard/category-stats');
      return response.data.data;
    } catch (error) {
      console.error('获取分类统计失败:', error);
      throw error;
    }
  }
};