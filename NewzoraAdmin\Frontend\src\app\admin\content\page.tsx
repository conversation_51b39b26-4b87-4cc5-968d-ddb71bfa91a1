'use client';

import React, { useState, useEffect } from 'react';
import { Search, Filter, Eye, Edit, Trash2, AlertTriangle } from 'lucide-react';
import DataTable from '@/components/admin/common/DataTable';
import { supabaseService } from '@/services/supabaseService';
import { DataTableColumn } from '@/types/admin';
import Link from 'next/link';

interface Article {
  id: string;
  title: string;
  content: string;
  author_id: string;
  category: string;
  published: boolean;
  views: number;
  likes: number;
  created_at: string;
  profiles: {
    username: string;
    display_name: string;
  };
}

const ContentPage: React.FC = () => {
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedArticles, setSelectedArticles] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 20
  });

  const columns: DataTableColumn<Article>[] = [
    {
      key: 'title',
      title: 'Article Info',
      render: (_, record) => (
        <div>
          <div className="font-medium text-gray-900 truncate max-w-xs">{record.title}</div>
          <div className="text-sm text-gray-500">Author: {record.profiles?.display_name || record.profiles?.username}</div>
        </div>
      )
    },
    {
      key: 'category',
      title: 'Category',
      sortable: true,
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
          {value || 'Uncategorized'}
        </span>
      )
    },
    {
      key: 'published',
      title: 'Status',
      sortable: true,
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
        }`}>
          {value ? 'Published' : 'Draft'}
        </span>
      )
    },
    {
      key: 'views',
      title: 'Statistics',
      render: (_, record) => (
        <div className="text-sm text-gray-900">
          <div>Views: {record.views || 0}</div>
          <div>Likes: {record.likes || 0}</div>
        </div>
      )
    },
    {
      key: 'created_at',
      title: 'Created',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString('en-US')
    },
    {
      key: 'id',
      title: 'Actions',
      render: (_, record) => (
        <div className="flex space-x-2">
          <button 
            onClick={(e) => {
              e.preventDefault();
              alert(`View article: ${record.title}`);
            }}
            className="text-blue-600 hover:text-blue-900 text-sm p-1"
            title="View"
          >
            <Eye className="w-4 h-4" />
          </button>
          <button 
            onClick={(e) => {
              e.preventDefault();
              alert(`Edit article: ${record.title}`);
            }}
            className="text-green-600 hover:text-green-900 text-sm p-1"
            title="Edit"
          >
            <Edit className="w-4 h-4" />
          </button>
          <button 
            onClick={(e) => {
              e.preventDefault();
              handleTogglePublish(record.id, record.published);
            }}
            className={`text-sm px-2 py-1 rounded ${
              record.published 
                ? 'bg-yellow-100 text-yellow-600 hover:bg-yellow-200' 
                : 'bg-green-100 text-green-600 hover:bg-green-200'
            }`}
          >
            {record.published ? 'Unpublish' : 'Publish'}
          </button>
          <button 
            onClick={(e) => {
              e.preventDefault();
              if (confirm(`Are you sure you want to delete article "${record.title}"?`)) {
                setArticles(prev => prev.filter(article => article.id !== record.id));
                alert('Article has been deleted');
              }
            }}
            className="text-red-600 hover:text-red-900 text-sm p-1"
            title="Delete"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      )
    }
  ];

  const fetchArticles = async () => {
    try {
      setLoading(true);
      const result = await supabaseService.getArticles({
        page: pagination.current,
        limit: pagination.pageSize,
        search: searchQuery,
        category: categoryFilter,
        published: statusFilter === 'published' ? true : statusFilter === 'draft' ? false : undefined
      });
      
      setArticles(result.articles);
      setPagination(prev => ({
        ...prev,
        total: result.pagination.total
      }));
    } catch (error) {
      console.error('Failed to fetch article list:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchArticles();
  }, [pagination.current, searchQuery, categoryFilter, statusFilter]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchArticles();
  };

  const handleTogglePublish = async (articleId: string, currentStatus: boolean) => {
    try {
      await supabaseService.updateArticleStatus(articleId, !currentStatus);
      // 更新本地数据
      setArticles(prev => prev.map(article => 
        article.id === articleId ? { ...article, published: !currentStatus } : article
      ));
      alert(`Article has been ${!currentStatus ? 'published' : 'unpublished'}`);
    } catch (error) {
      console.error('Failed to update article status:', error);
      alert('Operation failed, please try again');
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Content Management</h1>
        <div className="flex space-x-3">
          {/* 添加举报管理链接 */}
          <Link href="/admin/content/reports" className="btn-secondary flex items-center">
            <AlertTriangle className="w-4 h-4 mr-2" />
            Report Management
          </Link>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSearch} className="flex flex-wrap gap-4">
          <div className="flex-1 min-w-64">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search article titles..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="form-input pl-10"
              />
            </div>
          </div>
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="form-select w-32"
          >
            <option value="">All Categories</option>
            <option value="technology">Technology</option>
            <option value="lifestyle">Lifestyle</option>
            <option value="essay">Essay</option>
          </select>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="form-select w-32"
          >
            <option value="">All Status</option>
            <option value="published">Published</option>
            <option value="draft">Draft</option>
          </select>
          <button type="submit" className="btn-primary flex items-center">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </button>
        </form>
      </div>

      {/* 批量操作 */}
      {selectedArticles.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-800">
              Selected {selectedArticles.length} articles
            </span>
            <div className="flex space-x-2">
              <button className="btn-secondary text-sm">Bulk Publish</button>
              <button className="btn-secondary text-sm">Bulk Unpublish</button>
              <button className="btn-danger text-sm">Bulk Delete</button>
            </div>
          </div>
        </div>
      )}

      {/* 文章列表 */}
      <DataTable
        data={articles}
        columns={columns}
        loading={loading}
        pagination={{
          current: pagination.current,
          total: pagination.total,
          pageSize: pagination.pageSize,
          onChange: (page) => setPagination(prev => ({ ...prev, current: page }))
        }}
        rowSelection={{
          selectedRowKeys: selectedArticles,
          onChange: setSelectedArticles
        }}
      />
    </div>
  );
};

export default ContentPage;