'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  LayoutDashboard, 
  Users, 
  FileText, 
  MessageSquare, 
  BarChart3, 
  Settings, 
  ChevronDown,
  ChevronRight,
  DollarSign,
  AlertTriangle
} from 'lucide-react';

interface MenuItem {
  title: string;
  href?: string;
  icon: React.ReactNode;
  children?: MenuItem[];
}

const AdminSidebar: React.FC = () => {
  const pathname = usePathname();
  const [openMenus, setOpenMenus] = useState<Record<string, boolean>>({});

  // 默认展开包含当前路径的菜单
  useEffect(() => {
    const initOpenMenus: Record<string, boolean> = {};
    
    if (pathname?.startsWith('/admin/users')) {
      initOpenMenus.users = true;
    }
    
    if (pathname?.startsWith('/admin/content')) {
      initOpenMenus.content = true;
    }
    
    if (pathname?.startsWith('/admin/analytics')) {
      initOpenMenus.analytics = true;
    }
    
    setOpenMenus(initOpenMenus);
  }, [pathname]);

  const toggleMenu = (menuKey: string) => {
    setOpenMenus(prev => ({
      ...prev,
      [menuKey]: !prev[menuKey]
    }));
  };

  const menuItems: MenuItem[] = [
    {
      title: 'Dashboard',
      href: '/admin/dashboard',
      icon: <LayoutDashboard className="w-5 h-5" />
    },
    {
      title: 'User Management',
      icon: <Users className="w-5 h-5" />,
      children: [
        { title: 'User List', href: '/admin/users', icon: <div className="w-5 h-5" /> },
        { title: 'Role Management', href: '/admin/users/roles', icon: <div className="w-5 h-5" /> }
      ]
    },
    {
      title: 'Content Management',
      icon: <FileText className="w-5 h-5" />,
      children: [
        { title: 'Article Management', href: '/admin/content', icon: <div className="w-5 h-5" /> },
        { title: 'Comment Management', href: '/admin/content/comments', icon: <MessageSquare className="w-5 h-5" /> },
        { title: 'Report Management', href: '/admin/content/reports', icon: <AlertTriangle className="w-5 h-5" /> }
      ]
    },
    {
      title: 'Revenue Management',
      icon: <DollarSign className="w-5 h-5" />,
      children: [
        { title: 'Withdrawal Management', href: '/admin/monetization', icon: <div className="w-5 h-5" /> },
        { title: 'Ad Management', href: '/admin/monetization/ads', icon: <div className="w-5 h-5" /> }
      ]
    },
    {
      title: 'Data Analytics',
      icon: <BarChart3 className="w-5 h-5" />,
      children: [
        { title: 'User Analytics', href: '/admin/analytics/users', icon: <div className="w-5 h-5" /> },
        { title: 'Content Analytics', href: '/admin/analytics/content', icon: <div className="w-5 h-5" /> }
      ]
    },
    {
      title: 'System Settings',
      href: '/admin/settings',
      icon: <Settings className="w-5 h-5" />
    }
  ];

  const isActive = (href?: string) => {
    if (!href) return false;
    return pathname === href;
  };

  const isMenuActive = (item: MenuItem) => {
    if (isActive(item.href)) return true;
    
    if (item.children) {
      return item.children.some(child => isActive(child.href));
    }
    
    return false;
  };

  return (
    <aside className="w-64 bg-gray-800 h-full fixed left-0 top-16 overflow-y-auto">
      <nav className="p-4">
        <ul className="space-y-1">
          {menuItems.map((item, index) => (
            <li key={index}>
              {item.href ? (
                <Link
                  href={item.href}
                  className={`flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white rounded-lg ${
                    isActive(item.href) ? 'bg-blue-700 text-white' : ''
                  }`}
                >
                  <span className="mr-3">{item.icon}</span>
                  <span className="font-medium">{item.title}</span>
                </Link>
              ) : (
                <>
                  <button
                    onClick={() => toggleMenu(item.title)}
                    className={`w-full flex items-center justify-between px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white rounded-lg ${
                      isMenuActive(item) ? 'bg-gray-700 text-white' : ''
                    }`}
                  >
                    <div className="flex items-center">
                      <span className="mr-3">{item.icon}</span>
                      <span className="font-medium">{item.title}</span>
                    </div>
                    {openMenus[item.title] ? (
                      <ChevronDown className="w-4 h-4" />
                    ) : (
                      <ChevronRight className="w-4 h-4" />
                    )}
                  </button>
                  
                  {openMenus[item.title] && item.children && (
                    <ul className="ml-8 mt-1 space-y-1">
                      {item.children.map((child, childIndex) => (
                        <li key={childIndex}>
                          <Link
                            href={child.href || '#'}
                            className={`flex items-center px-4 py-2 text-sm text-gray-400 hover:bg-gray-700 hover:text-white rounded-lg ${
                              isActive(child.href) ? 'bg-gray-700 text-white' : ''
                            }`}
                          >
                            <span className="mr-3">{child.icon}</span>
                            <span>{child.title}</span>
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </>
              )}
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
};

export default AdminSidebar;